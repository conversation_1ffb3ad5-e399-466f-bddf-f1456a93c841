{"src/tests/test_main_flow.py": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_FAULTED-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_ENGRAVING_BUSY-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_UT_HT-12-UT]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_DIAMETER-6-100]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_LENGTH-6-200]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_SCAN_REQUEST-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_FAULTED-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_SCAN_BUSY-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_SCAN_FINISHED-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_START_ROTATION_REQUEST-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_READY-1-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_RADIUS-6-50]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_HEIGHT-6-100]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_ANGLE-6-90]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_OWNER_NAME-12-Test Owner]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_OWNER_CODE-6-12345]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER-12-SN123456]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME-12-Test Manufacturer]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE-6-54321]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER-12-MSN123456]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING-12-2023-01-01]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST-12-2023-06-01]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE-10-200.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_CAPACITY-10-50.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT-10-10.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_FAULTED-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_ENGRAVING_BUSY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_UT_HT-HT]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_DIAMETER-100]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_LENGTH-200]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_SCAN_REQUEST-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_FAULTED-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_BUSY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_FINISHED-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_START_ROTATION_REQUEST-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_READY-True]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_RADIUS-50]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_HEIGHT-100]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_ANGLE-90]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_OWNER_NAME-Test Owner]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_OWNER_CODE-12345]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER-SN123456]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME-Test Manufacturer]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE-54321]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER-MSN123456]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING-2023-01-01]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST-2023-06-01]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE-200.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_CAPACITY-50.0]": true, "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT-10.0]": true, "src/tests/test_comm_opcua_general.py::TestOPCUANodes": true, "src/tests/test_comm_opcua_general.py::test_client_initialization": true, "src/tests/test_comm_opcua_general.py::test_client_send_heartbeat": true, "src/tests/test_comm_opcua_general.py::test_client_send_all_valid_values": true, "src/tests/test_comm_opcua_general.py::test_client_send_invalid_node_id": true, "src/tests/test_comm_opcua_general.py::test_client_send_invalid_value": true, "src/tests/test_comm_opcua_general.py::test_client_receive_all_valid_values": true}