import asyncio
from inspect import isawaitable
from typing import Any, Type

from asyncua import Node, Server
from asyncua.ua import VariantType
from communication.botko_nodes import BotkoOPCUANode
from communication.opcua_nodes import OPCUANodes


async def generate_nodes(
    root: Node,
    address_space: int,
    config: list[dict[str, Any]],
):
    res: dict[str, Node] = {}

    for entry in config:
        ids = entry['node_id']
        if isinstance(ids, str):
            ids = [ids]
        node_type = VariantType[entry['type']]
        default = entry['default']

        for node_id in ids:
            assert node_id not in res, f'Duplicate node id: {node_id}'
            node = await root.add_variable(
                address_space, node_id, default, varianttype=node_type
            )
            await node.set_writable()
            if 'array_dimensions' in entry:
                await node.write_array_dimensions(entry['array_dimensions'])
            res[node_id] = node

    return res


async def generate_nodes_from_enum(
    root: Node, address_space: int, node_enum: Type[OPCUANodes]
) -> dict[str, Node]:
    """Generate nodes from an OPCUANodes enum class."""
    res: dict[str, Node] = {}

    # Map Python types to VariantType
    type_map = {
        bool: VariantType.Boolean,
        int: VariantType.Int32,
        str: VariantType.String,
        float: VariantType.Float,
    }

    # Map Python types to default values
    default_map = {bool: False, int: 0, str: '', float: 0.0}

    for member in node_enum:
        node = member.get_node(member.name)
        node_id = node.node_id
        data_type = node.data_type
        variant_type = type_map.get(data_type)

        if not variant_type:
            raise ValueError(f'Unsupported data type: {data_type}')

        default_value = default_map.get(data_type)

        node = await root.add_variable(
            address_space, node_id, default_value, varianttype=variant_type
        )
        await node.set_writable()
        res[node_id] = node

    return res


async def main():
    name = 'nippon_it'
    url = 'opc.tcp://localhost:4840/'

    myserver = Server()
    await myserver.init()

    myserver.set_endpoint(url=url)
    address_space = await myserver.register_namespace(uri=name)

    root = myserver.get_objects_node()

    nodes = await generate_nodes_from_enum(
        root,
        address_space,
        BotkoOPCUANode,
    )
    locals().update(nodes)
    print(nodes)

    async with myserver:
        while True:
            try:
                res = eval(await asyncio.get_running_loop().run_in_executor(None, input, '> '))

                if isawaitable(res):
                    res = await res
                if res is not None:
                    print(res)
            except Exception:
                # Print traceback
                import sys
                import traceback

                traceback.print_exc(file=sys.stdout)


# Run the main script
if __name__ == '__main__':
    asyncio.run(main())
