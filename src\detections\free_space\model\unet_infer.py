import albumentations as A
import cv2 as cv
import numpy as np
import segmentation_models_pytorch as smp
import torch

from src.utils.max_pool import crop_image_to_multiple

device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
assert device.type == 'cuda', (
    'GPU not found, please use a GPU to train the model or disable this line.'
)


test_transforms = A.Compose(
    [
        A.Normalize(),
    ]
)


def infer(img):
    model = smp.Unet(
        encoder_name='mobilenet_v2',
        encoder_weights=None,
        in_channels=1,
        classes=1,
    )
    model.load_state_dict(torch.load('src/detections/free_space/model/unet_model.pth'))
    model.to(device)
    model.eval()

    img = test_transforms(image=img)['image']

    img_tensor = img[np.newaxis, np.newaxis, :, :]
    image = torch.from_numpy(img_tensor).float().to(device)

    # Forward pass
    with torch.no_grad():  # Disable gradient computation for inference
        output = model(image)

    # Convert to numpy
    cpu_output = output.cpu().detach().numpy()[0, 0, :, :]
    return cpu_output


if __name__ == '__main__':
    from matplotlib import pyplot as plt

    from src.utils.max_pool import crop_image_to_multiple

    # Example usage: scale down before use!
    img = cv.imread(
        'data/OCR/20250415/images/images_biglabel/20250415_113705057796_prep.png',
        cv.IMREAD_GRAYSCALE,
    )
    img = crop_image_to_multiple(img, 4)
    img = cv.resize(img, (img.shape[1] // 4, img.shape[0] // 4))
    result = infer(img)
    binary_result = result > 0.5
    fig, ax = plt.subplots(2, 1, sharex=True, sharey=True, figsize=(20, 8))
    ax[0].imshow(img, cmap='viridis')
    ax[1].imshow(binary_result, cmap='viridis')
    plt.show()
