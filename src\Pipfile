[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[[source]]  
url = "https://__token__:${HV_PYPI_TOKEN}@git.heliovision.be/api/v4/groups/32/-/packages/pypi/simple"
verify_ssl = true  
name = "hv-pypi"

[packages]
loguru = "*"
open3d = "*"
numpy = "*"
opencv-python = "*"
matplotlib = "*"
pydantic = "*"
"heliovision.camera.base" = {version = "*", index = "hv-pypi"}
"heliovision.camera.base.internal" = {version = "*", index = "hv-pypi"}
"heliovision.camera.keyence.profiler" = {version = "~=1.0", index = "hv-pypi"}
"heliovision.communication.opcua" = {version = "*", index = "hv-pypi"}
"heliovision.gui" = { version = "==3.*, >=3.4.0", index = "hv-pypi" }
"heliovision.config-system" = {version = "*", index = "hv-pypi"}
numba = "*"

[dev-packages]
ruff = "==0.9.10"
pytest = "*"
coverage = "*"
pytest-asyncio = "*"
opcua-client = "*"

[requires]
python_version = "3.12"
