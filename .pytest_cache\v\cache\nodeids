["src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_DIAMETER-6-100]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_ENGRAVING_BUSY-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_FAULTED-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_LENGTH-6-200]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_SCAN_REQUEST-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.BOTKO_UT_HT-12-UT]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_ANGLE-6-90]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_HEIGHT-6-100]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_ENGRAVING_RADIUS-6-50]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_FAULTED-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_CAPACITY-10-50.0]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST-12-2023-06-01]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING-12-2023-01-01]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE-6-54321]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME-12-Test Manufacturer]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER-12-MSN123456]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT-10-10.0]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_OWNER_CODE-6-12345]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_OWNER_NAME-12-Test Owner]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_READY-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER-12-SN123456]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE-10-200.0]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_SCAN_BUSY-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_SCAN_FINISHED-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_receive[BotkoOPCUANode.HV_START_ROTATION_REQUEST-1-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_DIAMETER-100]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_ENGRAVING_BUSY-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_FAULTED-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_LENGTH-200]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_SCAN_REQUEST-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.BOTKO_UT_HT-HT]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_ANGLE-90]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_HEIGHT-100]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_ENGRAVING_RADIUS-50]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_FAULTED-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_CAPACITY-50.0]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST-2023-06-01]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING-2023-01-01]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE-54321]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME-Test Manufacturer]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER-MSN123456]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT-10.0]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_OWNER_CODE-12345]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_OWNER_NAME-Test Owner]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_READY-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER-SN123456]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE-200.0]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_BUSY-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_SCAN_FINISHED-True]", "src/tests/test_comm_opcua_botko.py::test_botko_client_send[BotkoOPCUANode.HV_START_ROTATION_REQUEST-True]", "src/tests/test_comm_opcua_general.py::test_client_initialization", "src/tests/test_comm_opcua_general.py::test_client_receive_all_valid_values", "src/tests/test_comm_opcua_general.py::test_client_send_all_valid_values", "src/tests/test_comm_opcua_general.py::test_client_send_heartbeat", "src/tests/test_comm_opcua_general.py::test_client_send_invalid_node_id", "src/tests/test_comm_opcua_general.py::test_client_send_invalid_value", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_boolean_validation", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_date_extraction_invalid", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_date_extraction_invalid_from_list", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_date_extraction_valid", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_from_list_edge_case_boundary", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_from_list_non_boolean_fields", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_from_list_parsing", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_from_list_with_expired_keur_data", "src/tests/test_input_data_excel_tree.py::TestInputDataExcelTree::test_from_list_with_non_expired_keur_data", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_approved_bottle_generates_engraving", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_default_rejection_status", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_drew_true_keurdatum_expired_no_pi_keur", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_drew_true_keurdatum_expired_pi_keur", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_drew_true_keurdatum_not_expired", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_both_hexagon_and_epsilon", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_hexagon_no_episilon", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_no_hexagon_but_episilon", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_after_1978_no_hexagon_or_episilon", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_no_pi_symbol_no_treksterkte_no_stoomwezen_e_keur_before_1978", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_no_pi_symbol_no_treksterkte_stoomwezen_rejection", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_pi_symbol_and_treksterkte_e_keur_after_1978", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_pi_symbol_and_treksterkte_e_keur_before_1978", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_pi_symbol_only", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_rejected_bottle_no_engraving", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_rejection_reason_property", "src/tests/test_output_data_excel_tree.py::TestOutputDataExcelTree::test_wanddikte_treksterkte_without_pi_symbol_raises_error", "src/tests/test_preferred_position.py::test_sort_1_element", "src/tests/test_preferred_position.py::test_sort_5_elements_with_max_candidates", "src/tests/test_preferred_position.py::test_sort_elements_different_sizes", "src/tests/test_preferred_position.py::test_sort_elements_same_size"]